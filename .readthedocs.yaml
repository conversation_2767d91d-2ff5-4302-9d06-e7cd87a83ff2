# Author: Dr. <PERSON><PERSON><PERSON>
# ReadTheDocs configuration for SemiPRO

version: 2

build:
  os: ubuntu-22.04
  tools:
    python: "3.9"
  jobs:
    post_create_environment:
      - pip install --upgrade pip setuptools wheel
    post_install:
      - pip install -r docs/requirements.txt

sphinx:
  configuration: docs/source/conf.py
  builder: html
  fail_on_warning: false

formats:
  - pdf
  - epub

python:
  install:
    - requirements: docs/requirements.txt
