# Author: <PERSON><PERSON> <PERSON>
# distutils: language = c++
# distutils: sources = ../cpp/core/wafer.cpp ../cpp/core/utils.cpp

from libcpp.memory cimport shared_ptr
from libcpp.string cimport string
from libcpp.vector cimport vector
from libcpp.pair cimport pair
cimport numpy as np
import numpy as np
from cython cimport view

cdef extern from "Eigen/Dense" namespace "Eigen":
    cdef cppclass ArrayXd:
        ArrayXd()
        ArrayXd(int)
        double& operator[](int)
        int size()
        double* data()

    cdef cppclass ArrayXXd:
        ArrayXXd()
        ArrayXXd(int, int)
        double& operator()(int, int)
        int rows()
        int cols()
        double* data()

cdef extern from "../cpp/core/wafer.hpp":
    cppclass Wafer:
        Wafer(double, double, string) except +
        void initializeGrid(int, int) except +
        void applyLayer(double, string) except +
        void setDopantProfile(const ArrayXd&) except +
        void setPhotoresistPattern(const ArrayXXd&) except +
        void addFilmLayer(double, string) except +
        void addMetalLayer(double, string) except +
        void addPackaging(double, string, vector[pair[pair[int, int], pair[int, int]]]) except +
        void setElectricalProperties(vector[pair[string, double]]) except +
        void setTemperatureProfile(const ArrayXXd&) except +
        void setThermalConductivity(const ArrayXXd&) except +
        void setElectromigrationMTTF(const ArrayXXd&) except +
        void setThermalStress(const ArrayXXd&) except +
        void setDielectricField(const ArrayXXd&) except +
        void updateGrid(const ArrayXXd&) except +
        ArrayXXd& getGrid() except +
        ArrayXd& getDopantProfile() except +
        ArrayXXd& getPhotoresistPattern() except +
        vector[pair[double, string]]& getFilmLayers() except +
        vector[pair[double, string]]& getMetalLayers() except +
        pair[double, string] getPackagingSubstrate() except +
        vector[pair[pair[int, int], pair[int, int]]]& getWireBonds() except +
        vector[pair[string, double]]& getElectricalProperties() except +
        ArrayXXd& getTemperatureProfile() except +
        ArrayXXd& getThermalConductivity() except +
        ArrayXXd& getElectromigrationMTTF() except +
        ArrayXXd& getThermalStress() except +
        ArrayXXd& getDielectricField() except +
        string getMaterialId() except +
        double getDiameter() except +
        double getThickness() except +

cdef extern from "../cpp/modules/geometry/geometry_manager.hpp":
    cppclass GeometryManager:
        GeometryManager() except +
        void initializeGrid(shared_ptr[Wafer], int, int) except +
        void applyLayer(shared_ptr[Wafer], double, string) except +

# Helper functions to convert between NumPy and Eigen
cdef ArrayXd numpy_to_eigen_1d(np.ndarray[np.float64_t, ndim=1] arr):
    cdef ArrayXd eigen_arr = ArrayXd(arr.shape[0])
    cdef int i
    for i in range(arr.shape[0]):
        eigen_arr[i] = arr[i]
    return eigen_arr

cdef ArrayXXd numpy_to_eigen_2d(np.ndarray[np.float64_t, ndim=2] arr):
    cdef ArrayXXd eigen_arr = ArrayXXd(arr.shape[0], arr.shape[1])
    cdef int i, j
    for i in range(arr.shape[0]):
        for j in range(arr.shape[1]):
            eigen_arr(i, j) = arr[i, j]
    return eigen_arr

cdef np.ndarray eigen_to_numpy_1d(const ArrayXd& eigen_arr):
    cdef int size = eigen_arr.size()
    cdef np.ndarray[np.float64_t, ndim=1] arr = np.zeros(size, dtype=np.float64)
    cdef int i
    for i in range(size):
        arr[i] = eigen_arr[i]
    return arr

cdef np.ndarray eigen_to_numpy_2d(const ArrayXXd& eigen_arr):
    cdef int rows = eigen_arr.rows()
    cdef int cols = eigen_arr.cols()
    cdef np.ndarray[np.float64_t, ndim=2] arr = np.zeros((rows, cols), dtype=np.float64)
    cdef int i, j
    for i in range(rows):
        for j in range(cols):
            arr[i, j] = eigen_arr(i, j)
    return arr

cdef class PyWafer:
    cdef shared_ptr[Wafer] thisptr

    def __cinit__(self, diameter: float, thickness: float, material_id: str):
        self.thisptr = shared_ptr[Wafer](new Wafer(diameter, thickness, material_id.encode('utf-8')))

    def initialize_grid(self, x_dim: int, y_dim: int):
        self.thisptr.get().initializeGrid(x_dim, y_dim)

    def apply_layer(self, thickness: float, material_id: str):
        self.thisptr.get().applyLayer(thickness, material_id.encode('utf-8'))

    def set_dopant_profile(self, np.ndarray[np.float64_t, ndim=1] profile):
        cdef ArrayXd eigen_profile = numpy_to_eigen_1d(profile)
        self.thisptr.get().setDopantProfile(eigen_profile)

    def set_photoresist_pattern(self, np.ndarray[np.float64_t, ndim=2] pattern):
        cdef ArrayXXd eigen_pattern = numpy_to_eigen_2d(pattern)
        self.thisptr.get().setPhotoresistPattern(eigen_pattern)

    def add_film_layer(self, thickness: float, material: str):
        self.thisptr.get().addFilmLayer(thickness, material.encode('utf-8'))

    def add_metal_layer(self, thickness: float, metal: str):
        self.thisptr.get().addMetalLayer(thickness, metal.encode('utf-8'))

    def add_packaging(self, substrate_thickness: float, substrate_material: str, wire_bonds: list):
        cdef vector[pair[pair[int, int], pair[int, int]]] cpp_bonds
        for bond in wire_bonds:
            x1, y1, x2, y2 = bond
            cpp_bonds.push_back(pair[pair[int, int], pair[int, int]](pair[int, int](x1, y1), pair[int, int](x2, y2)))
        self.thisptr.get().addPackaging(substrate_thickness, substrate_material.encode('utf-8'), cpp_bonds)

    def set_electrical_properties(self, properties: list):
        cdef vector[pair[string, double]] cpp_props
        for prop in properties:
            name, value = prop
            cpp_props.push_back(pair[string, double](name.encode('utf-8'), value))
        self.thisptr.get().setElectricalProperties(cpp_props)

    def set_temperature_profile(self, np.ndarray[np.float64_t, ndim=2] profile):
        cdef ArrayXXd eigen_profile = numpy_to_eigen_2d(profile)
        self.thisptr.get().setTemperatureProfile(eigen_profile)

    def set_thermal_conductivity(self, np.ndarray[np.float64_t, ndim=2] conductivity):
        cdef ArrayXXd eigen_conductivity = numpy_to_eigen_2d(conductivity)
        self.thisptr.get().setThermalConductivity(eigen_conductivity)

    def set_electromigration_mttf(self, np.ndarray[np.float64_t, ndim=2] mttf):
        cdef ArrayXXd eigen_mttf = numpy_to_eigen_2d(mttf)
        self.thisptr.get().setElectromigrationMTTF(eigen_mttf)

    def set_thermal_stress(self, np.ndarray[np.float64_t, ndim=2] stress):
        cdef ArrayXXd eigen_stress = numpy_to_eigen_2d(stress)
        self.thisptr.get().setThermalStress(eigen_stress)

    def set_dielectric_field(self, np.ndarray[np.float64_t, ndim=2] field):
        cdef ArrayXXd eigen_field = numpy_to_eigen_2d(field)
        self.thisptr.get().setDielectricField(eigen_field)

    def update_grid(self, np.ndarray[np.float64_t, ndim=2] grid):
        cdef ArrayXXd eigen_grid = numpy_to_eigen_2d(grid)
        self.thisptr.get().updateGrid(eigen_grid)
    def get_grid(self):
        cdef ArrayXXd& grid_ref = self.thisptr.get().getGrid()
        return eigen_to_numpy_2d(grid_ref)

    def get_dopant_profile(self):
        cdef ArrayXd& profile_ref = self.thisptr.get().getDopantProfile()
        return eigen_to_numpy_1d(profile_ref)

    def get_photoresist_pattern(self):
        cdef ArrayXXd& pattern_ref = self.thisptr.get().getPhotoresistPattern()
        return eigen_to_numpy_2d(pattern_ref)
    def get_film_layers(self):
        layers = self.thisptr.get().getFilmLayers()
        return [(layer.first, layer.second.decode('utf-8')) for layer in layers]
    def get_metal_layers(self):
        layers = self.thisptr.get().getMetalLayers()
        return [(layer.first, layer.second.decode('utf-8')) for layer in layers]
    def get_packaging_substrate(self):
        substrate = self.thisptr.get().getPackagingSubstrate()
        return (substrate.first, substrate.second.decode('utf-8'))
    def get_wire_bonds(self):
        bonds = self.thisptr.get().getWireBonds()
        return [((bond.first.first, bond.first.second), (bond.second.first, bond.second.second)) for bond in bonds]
    def get_electrical_properties(self):
        props = self.thisptr.get().getElectricalProperties()
        return [(prop.first.decode('utf-8'), prop.second) for prop in props]
    def get_temperature_profile(self):
        cdef ArrayXXd& profile_ref = self.thisptr.get().getTemperatureProfile()
        return eigen_to_numpy_2d(profile_ref)

    def get_thermal_conductivity(self):
        cdef ArrayXXd& conductivity_ref = self.thisptr.get().getThermalConductivity()
        return eigen_to_numpy_2d(conductivity_ref)

    def get_electromigration_mttf(self):
        cdef ArrayXXd& mttf_ref = self.thisptr.get().getElectromigrationMTTF()
        return eigen_to_numpy_2d(mttf_ref)

    def get_thermal_stress(self):
        cdef ArrayXXd& stress_ref = self.thisptr.get().getThermalStress()
        return eigen_to_numpy_2d(stress_ref)

    def get_dielectric_field(self):
        cdef ArrayXXd& field_ref = self.thisptr.get().getDielectricField()
        return eigen_to_numpy_2d(field_ref)
    def get_material_id(self):
        return self.thisptr.get().getMaterialId().decode('utf-8')
    def get_diameter(self):
        return self.thisptr.get().getDiameter()
    def get_thickness(self):
        return self.thisptr.get().getThickness()

cdef class PyGeometryManager:
    def __cinit__(self):
        self.thisptr = new GeometryManager()
    def __dealloc__(self):
        del self.thisptr
    def initialize_grid(self, PyWafer wafer, x_dim: int, y_dim: int):
        self.thisptr.initializeGrid(wafer.thisptr, x_dim, y_dim)
    def apply_layer(self, PyWafer wafer, thickness: float, material_id: str):
        self.thisptr.applyLayer(wafer.thisptr, thickness, material_id.encode('utf-8'))